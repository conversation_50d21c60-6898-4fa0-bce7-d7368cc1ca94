/* eslint-disable max-depth */
/**
 * 用于获取选中的label
 * @param {VueComponent} Component DepartmentPeopleSelector组件
 * @returns {String} 选中的label
 */
export const getSelectedLabel = (Component) => {
    const item = Component.$refs.refElSelect.selected;
    if (Array.isArray(item)) {
        return item.map((i) => i.label);
    }
    return item.label;
};

/**
 * 保存查询参数到 sessionStorage
 * @param {VueComponent} vm Vue组件实例
 * @param {String} storageKey 存储键名
 * @param {Object} options 配置选项
 * @param {Object} options.params 查询参数
 * @param {Number} options.page 当前页码
 * @param {Number} options.limit 每页条数
 * @param {String} options.sortOrder 排序方式
 * @param {String} options.sortKey 排序字段
 * @param {Object} options.extraFields 额外需要保存的字段
 */
export const saveParams = (vm, storageKey, options = {}) => {
    const {
        params = vm.params,
        page = vm.page,
        limit = vm.limit,
        sortOrder = vm.sortOrder,
        sortKey = vm.sortKey,
        activeNavTab = vm.activeNavTab,
        extraFields = {}
    } = options;

    const paramsToSave = {
        params,
        page,
        limit,
        sortOrder,
        sortKey,
        activeNavTab,
        ...extraFields
    };

    try {
        sessionStorage.setItem(storageKey, JSON.stringify(paramsToSave));
    } catch (error) {
        console.warn('保存查询参数失败:', error);
    }
};

/**
 * 从 sessionStorage 恢复查询参数
 * @param {VueComponent} vm Vue组件实例
 * @param {String} storageKey 存储键名
 * @param {Object} defaultParams 默认查询参数
 * @param {Object} options 配置选项
 * @param {Boolean} options.syncToQueryParams 是否同步到queryParams，默认true
 * @param {Object} options.extraFields 额外需要恢复的字段及其默认值
 * @param {String} options.defaultSortKey 默认排序字段，默认为'createDate'
 */
export const restoreParams = (vm, storageKey, defaultParams = {}, options = {}) => {
    const { syncToQueryParams = true, extraFields = {}, defaultSortKey = 'createDate' } = options;

    try {
        const savedParams = sessionStorage.getItem(storageKey);
        if (savedParams) {
            const parsedParams = JSON.parse(savedParams);

            // 恢复各个参数
            vm.params = parsedParams.params || {};
            vm.page = parsedParams.page || 1;
            vm.limit = parsedParams.limit || 10;
            vm.sortOrder = parsedParams.sortOrder || '';
            vm.sortKey = parsedParams.sortKey || defaultSortKey || '';
            vm.activeNavTab = parsedParams.activeNavTab || '';

            // 恢复额外字段
            Object.keys(extraFields).forEach((key) => {
                vm[key] = parsedParams[key] || extraFields[key];
            });

            // 同步到 queryParams 以便表单显示，如果navTab已选择，不对下方搜索条件进行赋值
            if (syncToQueryParams && vm.queryParams) {
                Object.assign(vm.queryParams, vm.params);
            }
        } else {
            // 如果没有保存的参数，使用默认值
            vm.params = vm.$tools ? vm.$tools.cloneDeep(defaultParams) : { ...defaultParams };

            // 设置额外字段的默认值
            Object.keys(extraFields).forEach((key) => {
                vm[key] = extraFields[key];
            });
        }
    } catch (error) {
        console.warn('恢复查询参数失败:', error);
        vm.params = vm.$tools ? vm.$tools.cloneDeep(defaultParams) : { ...defaultParams };

        // 设置额外字段的默认值
        Object.keys(extraFields).forEach((key) => {
            vm[key] = extraFields[key];
        });
    }
};

/**
 * 根据搜索条件同步导航状态
 * @param {VueComponent} vm Vue组件实例
 */
export const syncNavFromSearch = (vm) => {
    // 遍历导航项，找到与当前搜索条件匹配的导航项
    for (const navItem of vm.navItems) {
        const { field, queryField } = navItem;
        // 检查搜索条件中的状态是否与导航项匹配
        if (JSON.stringify(vm.queryParams[queryField]) === JSON.stringify(field)) {
            // 如果找到匹配的导航项，更新 activeNavTab
            if (vm.activeNavTab !== navItem.name) {
                vm.activeNavTab = navItem.name;
            }
            return;
        }
    }

    // 如果没有找到匹配项，将 activeNavTab 设置为空字符串
    vm.activeNavTab = '';
};
