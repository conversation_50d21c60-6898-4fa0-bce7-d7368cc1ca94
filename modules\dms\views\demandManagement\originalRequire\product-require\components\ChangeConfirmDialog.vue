<template>
    <el-dialog
        title="需求变更确认"
        :visible.sync="dialogVisible"
        width="800px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
    >
        <div class="change-confirm-content">
            <p>由于用户需求内容发生变更，请确认以下需求是否受到影响并要进行变更:</p>
            <p class="demand-name">{{ demandName }}</p>
            <a href="javascript:;" @click="handleViewDetail" class="detail-link">查看产品需求详情</a>
        </div>
        <!-- 对比描述列表 -->
        <el-descriptions class="change-descriptions" :column="1" border>
            <el-descriptions-item label="用户需求名称" labelClassName="desc-label">
                <div class="source-content">
                    {{ changeData.descriptionBefore }}
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="变更原因" labelClassName="desc-label">
                <div class="source-content">
                    {{ changeData.reason }}
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="变更项" labelClassName="desc-label">
                <div class="change-content">
                    <div class="change-row">
                        <div class="change-before">
                            <div class="change-label bold">变更前</div>
                        </div>
                        <div class="change-after">
                            <div class="change-label bold">变更后</div>
                        </div>
                    </div>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="需求描述" labelClassName="desc-label">
                <div class="change-content">
                    <div class="change-row">
                        <div class="change-before">
                            <div>{{ changeData.descriptionBefore }}</div>
                        </div>
                        <div class="change-after">
                            <div style="margin-left: 5px">{{ changeData.descriptionAfter }}</div>
                        </div>
                    </div>
                </div>
            </el-descriptions-item>
        </el-descriptions>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleNoImpact">没有影响</el-button>
            <el-button type="primary" @click="handleChangeConfirm">变更需求</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'ChangeConfirmDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        rowData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            // 变更详情数据
            changeData: {}
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        },
        demandName() {
            return this.rowData.demandName || '';
        }
    },
    watch: {
        visible: {
            handler(newVal) {
                if (newVal) {
                    this.getChangeDetail();
                }
            },
            immediate: true
        }
    },
    methods: {
        /**
         * 获取变更详情数据
         */
        async getChangeDetail() {
            if (!this.rowData.demandId) {
                return;
            }

            try {
                const api = this.$service.dms.demand.getUserOrProductDamandCheckDetail;
                const params = {
                    demandId: this.rowData.demandId
                };

                const res = await api(params);
                if (res.code === '0000' && res.data) {
                    this.changeData = res.data;
                }
            } catch (error) {
                console.error('获取变更详情失败:', error);
            }
        },
        /**
         * 查看详情
         */
        handleViewDetail() {
            this.$emit('view-detail', this.rowData);
            this.dialogVisible = false;
        },

        /**
         * 变更需求
         */
        async handleChangeConfirm() {
            try {
                const params = {
                    storyId: this.rowData.demandId,
                    checkId: this.rowData.checkId,
                    storyVersion: this.rowData.demandVersion,
                    status: '已接受',
                    checkAction: '变更需求',
                    reason: '由于用户需求内容发生变更，经确认，本需求也需要变更'
                };

                const res = await this.$service.dms.original.updateStoryCheck(params);
                if (res.code === '0000') {
                    this.$message.success(res.message);
                    this.$emit('success');
                    this.dialogVisible = false;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                console.error('变更需求失败:', error);
                this.$message.error('变更需求失败');
            }
        },

        /**
         * 没有影响
         */
        async handleNoImpact() {
            try {
                const params = {
                    storyId: this.rowData.demandId,
                    checkId: this.rowData.checkId,
                    storyVersion: this.rowData.demandVersion,
                    status: '已接受',
                    checkAction: '没有影响',
                    reason: '用户需求内容发生变更，经确认，本需求没有影响'
                };

                const res = await this.$service.dms.original.updateStoryCheck(params);
                if (res.code === '0000') {
                    this.$message.success(res.message);
                    this.$emit('success');
                    this.dialogVisible = false;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                console.error('确认没有影响失败:', error);
                this.$message.error('操作失败');
            }
        },

        /**
         * 关闭弹窗
         */
        handleClose() {
            this.dialogVisible = false;
            this.resetData();
        },

        /**
         * 重置数据
         */
        resetData() {
            this.changeData = {};
        }
    }
};
</script>

<style lang="scss" scoped>
.change-confirm-content {
    text-align: center;

    p {
        margin: 0 0 16px 0;
        line-height: 1.5;
    }

    .demand-name {
        font-weight: bold;
        color: #333;
        font-size: 14px;
        margin: 20px 0;
    }

    .detail-link {
        color: #5081fb;
        font-size: 12px;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

.dialog-footer {
    text-align: center;
}

.change-descriptions {
    width: 100%;
    margin: 20px 0;

    ::v-deep .el-descriptions__label {
        background-color: #f9f9f9;
        font-weight: bold;
        text-align: center;
        width: 120px;
        vertical-align: middle;
    }

    ::v-deep .el-descriptions__content {
        padding: 12px;
    }

    .source-content {
        line-height: 1.5;
    }

    .change-content {
        .change-row {
            display: flex;

            .change-before,
            .change-after {
                flex: 1;

                .change-label {
                    text-align: center;
                    line-height: 1.4;
                    word-break: break-all;
                }

                .bold {
                    font-weight: bold;
                }
            }

            .change-before {
                border-right: 1px solid #ddd;
            }
        }
    }
}

::v-deep .desc-label {
    background-color: #f9f9f9;
    font-weight: bold;
    text-align: center;
    width: 120px;
    vertical-align: middle;
}
</style>
