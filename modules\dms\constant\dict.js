// 需求类型
const typeData = [
    {
        label: '销售客户',
        value: '销售客户'
    },
    {
        label: '产品规划',
        value: '产品规划'
    },
    {
        label: '问题修复',
        value: '问题修复'
    },
    {
        label: '技术债务',
        value: '技术债务'
    },
    {
        label: '风险控制',
        value: '风险控制'
    }
];

// 优先级
const priorityData = [
    { label: '高', value: '高' },
    { label: '中', value: '中' },
    { label: '低', value: '低' }
];

// 需求等级
const storyLevelData = [
    { label: '一级', value: '一级' },
    { label: '二级', value: '二级' },
    { label: '三级', value: '三级' }
];

// 需求来源
const demandSourceData = [
    { label: '客户需求', value: '客户需求' },
    { label: '软硬项目', value: '软硬项目' },
    { label: '软件项目', value: '软件项目' },
    { label: '内部需求', value: '内部需求' }
];

// 原始需求状态
const originalDemandStatus = [
    { label: '草稿', value: '草稿' },
    { label: '待审核', value: '待审核' },
    { label: '已拒绝', value: '已拒绝' },
    { label: '未开始', value: '未开始' },
    { label: '已计划', value: '已计划' },
    { label: '进行中', value: '进行中' },
    { label: '退回中', value: '退回中' },
    { label: '已完成', value: '已完成' },
    { label: '已关闭', value: '已关闭' }
];

// 用户/产品需求状态
const userAndProductDemandStatus = [
    { label: '未开始', value: '未开始' },
    { label: '已计划', value: '已计划' },
    { label: '进行中', value: '进行中' },
    { label: '已完成', value: '已完成' },
    { label: '已关闭', value: '已关闭' }
];

// 需求进度
const demandProgressData = [
    { label: '按时完成', value: '按时完成' },
    { label: '延期进行中', value: '延期进行中' },
    { label: '延期完成', value: '延期完成' }
];

// 部署/发布状态
const deployOrPublishStatusData = [
    { label: '已部署', value: '已部署' },
    { label: '已发布', value: '已发布' }
];

// 审核类型
const checkTypeData = [
    { label: '需求审核', value: '需求审核' },
    { label: '内容变更', value: '内容变更' },
    { label: '负责人变更', value: '负责人变更' }
];

// 关闭原因
const closeReason = [
    { label: '已完成', value: '已完成' },
    { label: '已取消', value: '已取消' },
    { label: '重复', value: '重复' }
];

// 发布情况
const publistSituation = [
    {
        label: '已发布',
        value: '已发布'
    },
    {
        label: '未发布',
        value: '未发布'
    },
    {
        label: '部分发布',
        value: '部分发布'
    }
];

// 部署情况
const depolyStituation = [
    {
        label: '已部署',
        value: '已部署'
    },
    {
        label: '未部署',
        value: '未部署'
    },
    {
        label: '部分部署',
        value: '部分部署'
    }
];
// 产品状态
const statusData = [
    {
        label: '进行中',
        value: '进行中'
    },
    {
        label: '已暂停',
        value: '已暂停'
    },
    {
        label: '已关闭',
        value: '已关闭'
    }
];

// 项目来源
const projectSourceData = [
    { label: '年初规划', value: '年初规划' },
    { label: '上年延续', value: '上年延续' },
    { label: '规划外新增', value: '规划外新增' }
];

// 项目类型
const projectTypeData = [
    { label: '自主规划', value: '自主规划' },
    { label: '客户定制', value: '客户定制' },
    { label: '市场导入', value: '市场导入' }
];

// 项目规模
const projectScaleData = [
    { label: '大型项目', value: '大型项目' },
    { label: '中型项目', value: '中型项目' },
    { label: '小型项目', value: '小型项目' }
];

// 项目归属
const projectOwnershipData = [
    { label: '新北洋', value: '新北洋' },
    { label: '荣鑫', value: '荣鑫' },
    { label: '正棋', value: '正棋' },
    { label: '数码', value: '数码' },
    { label: '其他', value: '其他' }
];

// 市场定位
const marketPositionData = [
    { label: '国内', value: '国内' },
    { label: '海外', value: '海外' },
    { label: '全球', value: '全球' }
];

// 项目立项类型
const projectApprovalTypeData = [
    { label: '市场开发立项', value: '市场开发立项' },
    { label: '市场预研立项', value: '市场预研立项' },
    { label: '研发内部立项', value: '研发内部立项' }
];

// 公司立项等级
const companyApprovalLevelData = [
    { label: 'Ⅰ', value: 'Ⅰ' },
    { label: 'Ⅱ', value: 'Ⅱ' },
    { label: 'Ⅲ', value: 'Ⅲ' }
];

// 项目级别
const projectLevelData = [
    { label: '一级', value: '一级' },
    { label: '二级', value: '二级' },
    { label: '三级', value: '三级' }
];

// 项目难度等级
const projectDifficultyLevelData = [
    { label: 'A', value: 'A' },
    { label: 'B', value: 'B' },
    { label: 'C', value: 'C' }
];

// 团队级别
const groupLevelData = [
    { label: '一级', value: '一级' },
    { label: '二级', value: '二级' },
    { label: '三级', value: '三级' }
];

// 团队类型
const groupTypeData = [
    { label: '红头文团队', value: '红头文团队' },
    { label: '虚拟团队', value: '虚拟团队' }
];

// 团队状态
const groupStatusData = [
    { label: '进行中', value: '进行中' },
    { label: '关闭', value: '关闭' }
];

// 审核结果
const reviewResultData = [
    { label: '所有', value: ['待审核', '已通过', '已拒绝'] },
    { label: '待审核', value: ['待审核'] },
    { label: '已通过', value: ['已通过'] },
    { label: '已拒绝', value: ['已拒绝'] }
];

// 项目状态
const projectStatusData = [
    { label: '待启动', value: '待启动' },
    { label: '进行中', value: '进行中' },
    { label: '暂停', value: '暂停' },
    { label: '结项', value: '结项' },
    { label: '终止', value: '终止' }
];

// 进度状态
const processStatusData = [
    { label: '正常', value: '正常' },
    { label: '较低延期风险', value: '较低延期风险' },
    { label: '较高延期风险', value: '较高延期风险' },
    { label: '已延期', value: '已延期' }
];
export default {
    typeData,
    priorityData,
    storyLevelData,
    originalDemandStatus,
    userAndProductDemandStatus,
    demandSourceData,
    demandProgressData,
    deployOrPublishStatusData,
    checkTypeData,
    closeReason,
    publistSituation,
    depolyStituation,
    statusData,
    projectSourceData,
    projectTypeData,
    projectScaleData,
    projectOwnershipData,
    marketPositionData,
    projectApprovalTypeData,
    companyApprovalLevelData,
    projectLevelData,
    projectDifficultyLevelData,
    groupLevelData,
    groupTypeData,
    groupStatusData,
    reviewResultData,
    projectStatusData,
    processStatusData
};
