<!--计划管理 -->
<template>
    <div class="view">
        <div class="content">
            <formula-title :title="searchTitle"></formula-title>
            <el-form class="query-area" :model="form" label-width="100px">
                <div class="area-search">
                    <el-form-item label="快捷查询">
                        <el-radio-group v-model="form.dateType" size="medium" @input="handleRadio">
                            <el-radio-button
                                v-for="(item, index) in dateTypeOptions"
                                :key="index"
                                :label="item.dateType"
                                >{{ item.dateType }}</el-radio-button
                            >
                        </el-radio-group>
                        <el-date-picker
                            v-model="form.requestDate"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            size="small"
                            style="width: 300px; margin-left: 10px"
                            :unlink-panels="true"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <div>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery()"
                            >查询</el-button
                        >
                        <el-button icon="el-icon-refresh-right" size="mini" @click="handleReset()">重置</el-button>
                    </div>
                </div>
            </el-form>
            <formula-title :title="planTitle"></formula-title>
            <el-tabs v-model="activeTab">
                <el-tab-pane v-for="(tab, index) in tabList" :key="index" :label="tab.label" :name="tab.name">
                    <el-table :data="data" style="width: 100%" border @sort-change="sortChange">
                        <el-table-column
                            v-for="(column, index) in columns"
                            :key="index"
                            :label="column.label"
                            :prop="column.prop"
                            :width="column.width"
                            :sortable="column.sortable"
                            align="center"
                        >
                            <!-- 计划名称列添加自定义渲染 -->
                            <template slot-scope="scope">
                                <span
                                    v-if="column.prop === 'planName'"
                                    class="plan-name"
                                    @click="viewPlanDetail(scope.row)"
                                >
                                    {{ scope.row.planName }}
                                </span>
                                <!-- 需求情况列自定义渲染进度条 -->
                                <template v-else-if="column.prop === 'requirementSituation'">
                                    <div
                                        class="requirement-progress"
                                        v-if="scope.row.requirementSituation"
                                        @click="handleRequirementClick(scope.row, 'requirement')"
                                    >
                                        <div
                                            class="progress-segment"
                                            v-for="(seg, segIndex) in scope.row.requirementSituation.segments.filter(
                                                (seg) => seg > 0
                                            )"
                                            :key="segIndex"
                                            :style="{
                                                width: (seg / scope.row.requirementSituation.total) * 100 + '%',
                                                background: getSegmentColor(segIndex)
                                            }"
                                        >
                                            <span>{{ seg }}</span>
                                        </div>
                                    </div>
                                    <span v-else>
                                        {{ scope.row[column.prop] }}
                                    </span>
                                </template>
                                <template v-else-if="column.prop === 'taskSituation'">
                                    <div class="requirement-progress" v-if="scope.row.taskSituation">
                                        <div
                                            class="progress-segment"
                                            v-for="(seg, segIndex) in scope.row.taskSituation.segments.filter(
                                                (seg) => seg > 0
                                            )"
                                            :key="segIndex"
                                            :style="{
                                                width: (seg / scope.row.taskSituation.total) * 100 + '%',
                                                background: getSegmentColor(segIndex)
                                            }"
                                        >
                                            <span>{{ seg }}</span>
                                        </div>
                                    </div>
                                    <span v-else>
                                        {{ scope.row[column.prop] }}
                                    </span>
                                </template>
                                <!-- 工时情况列渲染ECharts双折线图 -->
                                <template v-else-if="column.prop === 'workHourSituation'">
                                    <div
                                        class="work-hour-chart"
                                        :ref="'chart_' + scope.$index"
                                        :style="{ height: '100px', width: '100%' }"
                                    >
                                        <!-- 图表容器 -->
                                    </div>
                                </template>
                                <span v-else>{{ scope.row[column.prop] }}</span>
                            </template>
                        </el-table-column>
                        <!-- 操作列 -->
                        <el-table-column label="操作" width="200" align="center">
                            <template slot-scope="scope">
                                <el-button @click="editRow(scope.row)" size="mini" type="primary">编辑</el-button>
                                <el-button @click="evaluteRow(scope.row)" size="mini" type="primary"
                                    >冲刺评价</el-button
                                >
                                <el-button @click="deleteRow(scope.row)" size="mini" type="danger">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        :page-sizes="[10, 20, 30, 50]"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :page-size="queryParams.size"
                        :current-page="queryParams.page"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>

        <!-- 需求情况弹窗 -->
        <el-dialog :visible.sync="dialogVisible" width="15%" center>
            <div class="dialog-content" v-if="currentRow && currentRow.requirementSituation">
                <div class="item">
                    <span>📝</span>
                    <span>任务总数量：{{ currentRow.requirementSituation.total }}</span>
                </div>
                <div class="item">
                    <span>✅</span>
                    <span>完成率：{{ calculateCompletionRate(currentRow.requirementSituation) }}%</span>
                </div>
                <div class="item" v-for="(seg, index) in currentRow.requirementSituation.segments" :key="index">
                    <span class="icon" :style="{ background: getSegmentColor(index) }"></span>
                    <span>{{ segmentLabels[index] }}：{{ seg }}</span>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import echarts from 'echarts';
import { getDateRange } from 'snbcCommon/common/picker-options.js';
import formulaTitle from '../components/formulaTitle.vue';

// 日期区间选择配置
const dateTypeOptions = [
    { dateType: '全周期', dateRange: getDateRange('今天') },
    { dateType: '上一年度', dateRange: getDateRange('本月') },
    { dateType: '本年度', dateRange: getDateRange('本月') },
    { dateType: '最近一年', dateRange: getDateRange('本月') }
];

export default {
    name: 'PlannedManagement',
    components: { formulaTitle },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            searchTitle: '查询条件',
            planTitle: '计划列表',
            form: {
                dateType: '全周期',
                requestDate: getDateRange('今天')
            },
            dateTypeOptions,
            activeTab: 'table1',
            columns: [
                { label: '计划名称', prop: 'planName', width: '160', sortable: 'true' },
                { label: '计划开始日期', prop: 'planStartDate', width: '130', sortable: 'true' },
                { label: '计划完成日期', prop: 'planEndDate', width: '120' },
                { label: '实际完成日期', prop: 'actualEndDate', width: '120' },
                { label: '关闭日期', prop: 'closeDate', width: '120' },
                { label: '状态', prop: 'status', width: '100', sortable: 'true' },
                { label: '冲刺结果', prop: 'sprintResult', width: '100' },
                { label: '变更次数', prop: 'changeCount', width: '120', sortable: 'true' },
                { label: '需求情况', prop: 'requirementSituation', width: '200' },
                { label: '任务情况', prop: 'taskSituation', width: '200' },
                { label: '工时情况', prop: 'workHourSituation', width: '200' }
            ],
            tabList: [
                {
                    label: '所有',
                    name: 'table1'
                },
                {
                    label: '进行中',
                    name: 'table2'
                },
                {
                    label: '待启动',
                    name: 'table3'
                },
                {
                    label: '冲刺成功',
                    name: 'table4'
                },
                {
                    label: '冲刺失败',
                    name: 'table5'
                },
                {
                    label: '结论待定',
                    name: 'table6'
                }
            ],
            data: [
                {
                    planName: '信息化V2.7.0',
                    planStartDate: '2025/6/1',
                    planEndDate: '2025/8/30',
                    actualEndDate: '',
                    closeDate: '',
                    status: '草稿',
                    sprintResult: '0',
                    changeCount: '0',
                    taskSituation: {
                        segments: [5, 2, 3, 0],
                        total: 10
                    },
                    workHourSituation: {
                        planned: [20, 30, 25, 35, 40],
                        actual: [18, 32, 28, 30, 38],
                        labels: ['第1周', '第2周', '第3周', '第4周', '第5周']
                    },
                    requirementSituation: {
                        segments: [3, 2, 5, 0],
                        total: 10
                    }
                },
                {
                    planName: '信息化V2.6.0',
                    planStartDate: '2025/5/1',
                    planEndDate: '2025/8/30',
                    actualEndDate: '',
                    closeDate: '',
                    status: '进行中',
                    sprintResult: '待定',
                    changeCount: '1',
                    requirementSituation: {
                        segments: [7, 2, 1, 0],
                        total: 10
                    },
                    taskSituation: {
                        segments: [6, 1, 3, 0],
                        total: 10
                    },
                    workHourSituation: {
                        planned: [40, 45, 50, 45],
                        actual: [38, 42, 48, 40],
                        labels: ['第1周', '第2周', '第3周', '第4周']
                    }
                },
                {
                    planName: '信息化V2.5.0',
                    planStartDate: '2025/4/1',
                    planEndDate: '2025/4/30',
                    actualEndDate: '2025/4/30',
                    closeDate: '关闭',
                    status: '待定',
                    sprintResult: '0',
                    changeCount: '0',
                    requirementSituation: {
                        segments: [4, 2, 3, 1],
                        total: 10
                    },
                    taskSituation: { segments: [5, 1, 3, 1], total: 10 },
                    workHourSituation: {
                        planned: [30, 35, 30, 25],
                        actual: [32, 38, 35, 28],
                        labels: ['第1周', '第2周', '第3周', '第4周']
                    }
                },
                {
                    planName: '信息化V2.4.0',
                    planStartDate: '2025/3/1',
                    planEndDate: '2025/3/30',
                    actualEndDate: '2025/3/30',
                    closeDate: '关闭',
                    status: '成功',
                    sprintResult: '0',
                    changeCount: '0',
                    requirementSituation: {
                        segments: [8, 1, 1, 0],
                        total: 10
                    },
                    taskSituation: { segments: [8, 1, 1, 0], total: 10 },
                    workHourSituation: {
                        planned: [25, 30, 35, 30, 25],
                        actual: [24, 32, 38, 35, 28],
                        labels: ['第1周', '第2周', '第3周', '第4周', '第5周']
                    }
                }
            ],
            // 分页
            queryParams: {
                page: 1,
                size: 20
            },
            total: 0,
            // 存储图表实例，用于销毁
            chartInstances: {},
            // 弹窗相关
            dialogVisible: false,
            currentRow: null,
            segmentLabels: ['已按时完成', '已延期完成', '正常进行中', '延期进行中']
        };
    },
    mounted() {
        // 使用$nextTick确保DOM已完全渲染
        this.$nextTick(() => {
            this.initAllCharts();
        });
    },
    updated() {
        // 使用$nextTick确保DOM更新完成后再重新渲染图表
        this.$nextTick(() => {
            this.redrawAllCharts();
        });
    },
    beforeDestroy() {
        this.destroyAllCharts();
    },
    methods: {
        handleRadio(dateType) {
            const target = this.dateTypeOptions.find((item) => item.dateType === dateType);
            this.$set(this.form, 'requestDate', target.dateRange);
        },
        // 新增：处理计划名称点击事件
        viewPlanDetail(row) {
            console.log('查看计划详情', row);
        },
        // 分页
        sizeChange(newSize) {
            this.queryParams.size = newSize;
        },
        currentChange(newPage) {
            this.queryParams.page = newPage;
        },
        // 需求计划
        getSegmentColor(index) {
            const colorMap = ['#32CD32', '#00BFFF', '#FFA500', '#FF0000'];
            return colorMap[index] || 'gray';
        },
        // 处理需求情况点击
        handleRequirementClick(row, type) {
            // 校验数据完整性
            if (
                row.requirementSituation &&
                row.requirementSituation.segments?.length === 4 &&
                row.requirementSituation.total > 0
            ) {
                this.currentRow = row;
                this.dialogVisible = true;
            } else {
                this.$message.warning('数据格式异常，无法展示详情');
            }
        },
        // 计算完成率
        calculateCompletionRate(situation) {
            const completed = situation.segments[0] + situation.segments[1];
            return ((completed / situation.total) * 100).toFixed(0);
        },
        // 表格排序处理
        sortChange() {
            // 表格排序后需要重新渲染图表
            this.$nextTick(() => {
                this.redrawAllCharts();
            });
        },
        // 初始化所有图表
        initAllCharts() {
            this.data.forEach((row, index) => {
                if (row.workHourSituation) {
                    this.initChart(index);
                }
            });
        },
        // 初始化单个图表
        initChart(rowIndex) {
            // 使用动态ref名称获取图表容器
            const chartDom = this.$refs[`chart_${rowIndex}`];
            if (!chartDom || !chartDom[0]) return;

            // 避免重复初始化
            if (this.chartInstances[rowIndex]) {
                this.chartInstances[rowIndex].dispose();
            }

            const workData = this.data[rowIndex].workHourSituation;
            if (!workData) return;

            const chart = echarts.init(chartDom[0]);

            const option = {
                grid: {
                    left: '10%',
                    right: '10%',
                    top: '10%',
                    bottom: '10%'
                },
                xAxis: {
                    type: 'category',
                    data: workData.labels,
                    axisLabel: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        show: false
                    }
                },
                series: [
                    {
                        name: '计划工时',
                        data: workData.planned,
                        type: 'line',
                        smooth: true,
                        color: '#32CD32',
                        lineStyle: {
                            type: 'dashed'
                        },
                        showSymbol: false
                    },
                    {
                        name: '实际工时',
                        data: workData.actual,
                        type: 'line',
                        smooth: true,
                        color: '#3370ff',
                        showSymbol: false
                    }
                ]
            };

            chart.setOption(option);
            this.chartInstances[rowIndex] = chart;
        },
        // 重新渲染所有图表
        redrawAllCharts() {
            this.data.forEach((_, index) => {
                this.initChart(index);
            });
        },
        // 销毁所有图表
        destroyAllCharts() {
            Object.values(this.chartInstances).forEach((chart) => {
                if (chart) {
                    chart.dispose();
                }
            });
            this.chartInstances = {};
        },
        // 冲刺评价
        evaluteRow(row) {
            this.$router.push({
                path: './PlanEvalute'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .query-area {
    margin-top: 10px;
    .area-search {
        height: 50px;
        display: flex;
        justify-content: space-between;
    }
}
// 表格列可点击样式
.plan-name {
    color: #3370ff;
    cursor: pointer;
}
.requirement-progress {
    display: flex;
    cursor: pointer;
    height: 24px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .progress-segment {
        display: flex;
        justify-content: center;
        align-items: center;

        span {
            font-size: 12px;
            color: white;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        }
    }
}
// 工时图表样式
.work-hour-chart {
    width: 100%;
    height: 50px;
}
// 弹窗样式
.dialog-content {
    .item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 6px 0;
    }
    .icon {
        width: 16px;
        height: 16px;
        margin-right: 10px;
        border-radius: 3px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
}
</style>
