<template>
    <div class="example-container">
        <demand-list
            :key="demandListKey"
            ref="demandList"
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :data="treeData"
            :actions-width="170"
            :default-search-open="false"
            :selectable="checkRowSelectable"
            :total="total"
            :page.sync="currentPage"
            :limit.sync="pageSize"
            :enable-lazy-load="true"
            :load-children="loadDemandChildren"
            :table-attrs="{ height: 'calc(100vh - 210px)' }"
            @pagination="query"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @sort-change="handleSortChange"
            @selection-change="handleSelectionChange"
        >
            <template #rightNav>
                <el-button type="text" @click="batchClose" style="margin-right: 10px"> 批量关闭 </el-button>
            </template>
            <template #demandName="{ row }">
                <div class="demand-name-wrapper">
                    <el-tooltip :content="row.demandName" placement="top" :disabled="!row.demandName">
                        <div class="demand-name-container">
                            <svg-icon :icon-class="computedDemandNameIcon(row)" class="svg-icon"></svg-icon>
                            <span class="demand-name-text">{{ row.demandName }}</span>
                            <span class="demand-name-suffix" v-if="row.demandClass !== '产品需求'">
                                <svg-icon
                                    icon-class="dms-demand-tree-numbers"
                                    class="svg-icon"
                                    style="width: 10px"
                                ></svg-icon>
                                {{ row.finishCount || 0 }}/{{ row.allCount || 0 }}
                            </span>
                            <svg-icon
                                v-if="isPermission"
                                :icon-class="computedplIcon(row)"
                                class="copy-icon"
                                @click="handlebatchCreat(row)"
                            ></svg-icon>
                        </div>
                    </el-tooltip>
                </div>
            </template>
            <!-- 操作列 -->
            <template #actions="{ row }">
                <div v-if="row.demandClass === '用户需求'">
                    <el-button type="text" @click="getDetail(row)">详情</el-button>
                    <el-button
                        type="text"
                        @click="handleEdit(row)"
                        v-if="row.demandStatus !== '已关闭' && row.checkStatus !== '待审核'"
                        >编辑</el-button
                    >
                    <el-button
                        type="text"
                        @click="handleChange(row)"
                        v-if="row.demandStatus !== '已关闭' && row.checkStatus !== '待审核'"
                        >变更</el-button
                    >
                    <el-button
                        type="text"
                        @click="handlechangeConfirm(row)"
                        v-if="row.demandStatus !== '已关闭' && row.checkStatus === '待审核'"
                        >变更确认</el-button
                    >
                    <el-button
                        type="text"
                        @click="handleClose(row)"
                        v-if="row.demandStatus !== '已关闭' && row.checkStatus !== '待审核'"
                        >关闭</el-button
                    >
                </div>
                <div v-else>
                    <el-button type="text" @click="getDetail(row)">详情</el-button>
                </div>
            </template>
        </demand-list>
        <OriginalDemand :visible.sync="originalDemandDialogVisible" :demandId="currentDemandId"></OriginalDemand>
        <SubOriginalDemand
            :visible.sync="subOriginalDemandDialogVisible"
            :demandId="currentDemandId"
        ></SubOriginalDemand>
        <ProductDemand :visible.sync="productDemandDialogVisible" :demandId="currentDemandId"></ProductDemand>
        <UserDemand :visible.sync="userDemandDialogVisible" :demandId="currentDemandId"></UserDemand>
        <create-require-pop ref="createRef"></create-require-pop>
        <edit-change-pop ref="editChangeRef"></edit-change-pop>
        <change-confirm-pop ref="changeconfirmRef"></change-confirm-pop>
        <!-- 关闭需求弹窗 -->
        <CloseDialog
            :visible.sync="closeDialogVisible"
            :demandId="currentDemandId"
            :rowData="currentRow"
            @success="query"
            @view-detail="handleViewDetailFromClose"
            :isMultiple="isMultiple"
            :rowDataList="selectedRows"
        ></CloseDialog>
        <!-- 变更确认弹窗 -->
        <ChangeConfirmDialog
            :visible.sync="changeConfirmDialogVisible"
            :rowData="currentRow"
            @success="handleChangeConfirmSuccess"
            @view-detail="getDetail"
        ></ChangeConfirmDialog>
    </div>
</template>

<script>
import DemandList from 'dms/views/demandManagement/components/demandList/index.vue';
import { queryParams, queryConfig, navItems } from './formInit.js';
import OriginalDemand from 'dms/views/demandManagement/demandDetail/OriginalDemand.vue';
import SubOriginalDemand from 'dms/views/demandManagement/demandDetail/SubOriginalDemand.vue';
import ProductDemand from 'dms/views/demandManagement/demandDetail/ProductDemand.vue';
import UserDemand from 'dms/views/demandManagement/demandDetail/UserDemand.vue';
import createRequirePop from '../components/createRequirePop.vue';
import editChangePop from './components/editChangePop.vue';
import changeConfirmPop from './components/changeConfirmPop.vue';
import CloseDialog from 'dms/views/demandManagement/components/CloseDialog.vue';
import ChangeConfirmDialog from './components/ChangeConfirmDialog.vue';
import { syncNavFromSearch } from 'dms/mixins/common.js';

export default {
    name: 'UserRequire',
    components: {
        DemandList,
        OriginalDemand,
        SubOriginalDemand,
        ProductDemand,
        UserDemand,
        createRequirePop,
        editChangePop,
        changeConfirmPop,
        CloseDialog,
        ChangeConfirmDialog
    },
    data() {
        return {
            isPermission: null,
            // 当前激活的导航标签
            activeNavTab: '未开始',
            params: { demandStatus: '未开始' },
            // 顶部查询栏配置
            navItems,
            queryParams,
            // 选中的行数据
            selectedRows: [],
            queryConfig,
            // 表格数据
            treeData: [],
            sortOrder: 'descending',
            sortKey: 'proposalStartTime',
            currentPage: 1,
            pageSize: 20,
            total: 100,
            // 原始需求弹窗
            originalDemandDialogVisible: false,
            // 子原始需求弹窗
            subOriginalDemandDialogVisible: false,
            // 产品需求弹窗
            productDemandDialogVisible: false,
            // 用户需求弹窗
            userDemandDialogVisible: false,
            // 当前选中的需求ID
            currentDemandId: '',
            // 当前行数据
            currentRow: {},
            // 关闭需求弹窗
            closeDialogVisible: false,
            // 变更确认弹窗
            changeConfirmDialogVisible: false,
            // 是否选中多个需求
            isMultiple: false,
            // 弹窗
            createTitle: '批量创建用户需求',
            commonTitle: '编辑用户需求',
            demandListKey: 0
        };
    },
    watch: {
        'queryParams.productLine': {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.getProductOptions(newVal);
                }
            }
        }
    },
    created() {
        this.getProjectOrGroup();
        this.query();
        this.getPermission();
    },
    methods: {
        // 查询权限
        getPermission() {
            this.$service.dms.original.updatePermission().then((res) => {
                if (res.code === '0000') {
                    this.isPermission = res.data;
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        /**
         * 获取项目或团队
         */
        async getProjectOrGroup() {
            const api = this.$service.dms.common.getProjectOrGroupList;
            const params = {};
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.queryConfig.items.find((i) => i.modelKey === 'assProjectId').elOptions = res.data.map((i) => ({
                    label: i.projectName,
                    value: i.projectId
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取产品下拉选项
         * @param {String} productLine  产品线
         */
        async getProductOptions(productLine) {
            this.queryParams.assProductId = '';
            try {
                const params = {
                    statusList: ['进行中'],
                    productLine
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.queryConfig.items.find((i) => i.modelKey === 'assProductId').elOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.id
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
            }
        },
        /**
         * 查询
         */
        async query() {
            try {
                const params = {
                    ...this.params,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder,
                    // 此项必填
                    storyClass: '用户需求',
                    currentPage: this.currentPage,
                    pageSize: this.pageSize
                };
                const api = this.$service.dms.original.getOriginalList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                // 处理数据，为懒加载准备
                this.treeData = this.processTreeData(res.data.list || []);
                this.total = res.data.total || 0;
                // 无法修改内部子节点懒加载状态，被迫使用key更新整个列表
                this.demandListKey += 1;
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },

        /**
         * 处理树状数据，为懒加载做准备
         * @param {Array} data - 原始数据
         * @returns {Array} 处理后的数据
         */
        processTreeData(data) {
            return data.map((item) => {
                const processedItem = { ...item };
                // 如果有子节点，设置hasChildren为true，并清空children（懒加载时再获取）
                if (item.allCount && item.allCount > 0) {
                    processedItem.hasChildren = true;
                } else {
                    processedItem.hasChildren = false;
                }
                return processedItem;
            });
        },

        /**
         * 懒加载子节点数据
         * @param {Object} tree - 当前节点数据
         * @param {Object} treeNode - 树节点对象
         * @returns {Promise<Array>} 子节点数据
         */
        async loadDemandChildren(tree, treeNode) {
            try {
                const params = {
                    demandId: tree.demandId,
                    storyClass: this.getNextDemandClass(tree.demandClass),
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder
                };

                // 调用API获取子节点数据
                const api = this.$service.dms.demand.getDemandChildren;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return [];
                }
                // 递归处理子节点数据
                return this.processTreeData(res.data || []);
            } catch (error) {
                console.error('加载子节点数据失败:', error);
            }
        },
        /**
         * 获取下一个级别的需求
         * @param {String} currentDemand 当前需求
         * @returns {String} 需求
         */
        getNextDemandClass(currentDemand) {
            if (currentDemand === '原始需求') {
                return '子原始需求';
            } else if (currentDemand === '子原始需求') {
                return '用户需求';
            }
            return '产品需求';
        },
        /**
         * 处理搜索
         * @param {Object} searchData - 搜索表单数据
         */
        handleSearch(searchData) {
            const { searchParams } = searchData;
            const { proposalTime, expectedDate } = searchParams;
            this.params = {
                ...searchParams,
                // 需求提出/创建开始日期
                proposalStartTime: proposalTime[0] || '',
                // 需求提出/创建结束日期
                proposalEndTime: proposalTime[0] || '',
                // 期望交付时间
                expectedStartDate: expectedDate[0] || '',
                // 期望交付时间
                expectedEndDate: expectedDate[0] || ''
            };
            this.currentPage = 1;
            // 搜索时同步导航状态 - 根据搜索条件自动设置对应的导航标签
            syncNavFromSearch(this);
            this.query();
        },
        /**
         * 处理重置
         */
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            // 重置时重置到第一页
            this.currentPage = 1;
            this.query();
        },
        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.queryParams[queryField] = field;
            this.params = this.$tools.cloneDeep(this.queryParams);
            this.query();
        },
        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息 { column, prop, order }
         */
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            this.sortKey = prop;
            this.sortOrder = order;
            this.query();
        },

        /**
         * 跳转至详情页面
         * @param {Object} row - 行数据
         */
        getDetail(row) {
            this.currentDemandId = row.demandId;
            if (row.demandClass === '原始需求') {
                this.originalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '子原始需求') {
                this.subOriginalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '用户需求') {
                this.userDemandDialogVisible = true;
                return;
            }
            this.productDemandDialogVisible = true;
        },
        /**
         * 选择项变化
         * @param {Array} selections 选中的行数据
         */
        handleSelectionChange(selections) {
            this.selectedRows = selections;
        },
        // 编辑
        handleEdit(row) {
            this.$refs.editChangeRef.open(row, 'edit');
        },
        // 变更
        handleChange(row) {
            this.$refs.editChangeRef.open(row, 'change');
        },
        // 变更确认
        handlechangeConfirm(row, type) {
            if (row.checkType === '内容变更' || row.checkType === '负责人变更') {
                this.currentRow = { ...row, type };
                this.changeConfirmDialogVisible = true;
            }
        },
        /**
         * 处理变更确认成功
         * @param {Object} data - 包含action和rowData的对象
         */
        handleChangeConfirmSuccess(data) {
            const { action, rowData } = data;
            // 如果是内容变更且选择了变更需求，需要打开变更弹窗
            if (action === 'change' && rowData) {
                this.$refs.changeconfirmRef.open(rowData, 'change');
            }
            // 刷新列表
            this.query();
        },
        /**
         * 计算需求名称图标
         * @param {Object} row 行数据
         * @return {String} 图标名称
         */
        computedDemandNameIcon(row) {
            if (row.demandClass === '用户需求') {
                return 'dms-demand-user-demand';
            }
            return 'dms-demand-product-demand';
        },
        computedIcon(row) {
            if (row.demandClass === '用户需求') {
                return 'cj';
            }
            return '';
        },
        computedplIcon(row) {
            if (row.demandClass === '用户需求') {
                return 'plcj';
            }
            return '';
        },
        // 快捷创建
        handleCreat(row) {
            this.$router.push({
                path: './CreateRequire',
                query: { demandClass: row.demandClass, type: 'kj' }
            });
        },
        // 非快捷创建
        handlebatchCreat(row) {
            this.$refs.createRef.open(row);
        },
        /**
         * 关闭需求
         * @param {Object} row - 行数据
         */
        handleClose(row) {
            this.isMultiple = false;
            this.currentDemandId = row.demandId;
            this.currentRow = row;
            this.closeDialogVisible = true;
        },
        /**
         * 从关闭弹窗查看需求详情
         * @param {Object} data - 包含demandId和demandClass的对象
         */
        handleViewDetailFromClose(data) {
            const { demandId, demandClass } = data;
            this.currentDemandId = demandId;
            if (demandClass === '原始需求') {
                this.originalDemandDialogVisible = true;
            } else if (demandClass === '子原始需求') {
                this.subOriginalDemandDialogVisible = true;
            } else if (demandClass === '用户需求') {
                this.userDemandDialogVisible = true;
            } else if (demandClass === '产品需求') {
                this.productDemandDialogVisible = true;
            }
        },
        batchClose() {
            if (this.selectedRows.length === 0) {
                this.$message.warning('请先选择需求');
                return;
            }
            this.isMultiple = true;
            this.closeDialogVisible = true;
        },
        /**
         * 检查行是否可选择
         * @param {Object} row 行数据
         * @param {Number} index 行索引
         * @return {Boolean} 是否可选择
         */
        checkRowSelectable(row, index) {
            if (row.demandStatus === '已关闭') {
                return false;
            }
            return true;
        }
    }
};
</script>

<style lang="scss" scoped>
.example-container {
    padding: 0 20px;
}
.svg-icon {
    padding: 0;
}

.demand-name-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 32px;
}

.demand-name-container {
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden;
    flex: 1;

    .demand-name-text {
        width: calc(100% - 70px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 5px;
    }

    .demand-name-suffix {
        font-size: 10px;
        position: absolute;
        right: 0;
        color: #999;
        flex-shrink: 0;
        white-space: nowrap;
    }

    .copy-icon {
        width: 20px;
        height: 20px;
        // margin-right: 35px;
        cursor: pointer;
    }
}

// 确保树状表格的展开箭头和内容在同一行
::v-deep .el-table__row {
    .el-table__expand-column {
        .el-table__expand-icon {
            display: inline-flex;
            align-items: center;
            vertical-align: middle;
        }
    }
}

// 只对包含需求名称的列应用 flex 布局，避免影响其他列的 showOverflowTooltip
::v-deep td .cell:has(.demand-name-wrapper) {
    display: flex;
    align-items: center;
    line-height: 1.5;
    min-height: 32px;
    position: relative;
}
</style>
