<!-- 冲刺评价 -->
<template>
    <div class="view">
        <div class="content">
            <formula-title :title="searchTitle"></formula-title>
            <el-form class="query-area" :model="form" label-width="100px">
                <div class="area-search">
                    <el-form-item label="快捷查询">
                        <el-radio-group v-model="form.dateType" size="medium" @input="handleRadio">
                            <el-radio-button
                                v-for="(item, index) in dateTypeOptions"
                                :key="index"
                                :label="item.dateType"
                                >{{ item.dateType }}</el-radio-button
                            >
                        </el-radio-group>
                        <el-date-picker
                            v-model="form.requestDate"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            size="small"
                            style="width: 300px; margin-left: 10px"
                            :unlink-panels="true"
                        ></el-date-picker>
                    </el-form-item>
                    <div>
                        <el-button type="primary" icon="el-icon-search" size="mini">查询</el-button>
                        <el-button icon="el-icon-refresh-right" size="mini">重置</el-button>
                    </div>
                </div>
            </el-form>
            <formula-title :title="planTitle"></formula-title>
            <el-tabs v-model="activeTab">
                <el-tab-pane v-for="(tab, index) in tabList" :key="index" :label="tab.label" :name="tab.name">
                    <el-table :data="tab.data" style="width: 100%" border>
                        <!-- 按目标表格调整 columns 与 prop 映射 -->
                        <el-table-column
                            v-for="(column, index) in columns"
                            :key="index"
                            :label="column.label"
                            :prop="column.prop"
                            :width="column.width"
                            align="center"
                        ></el-table-column>
                        <!-- 操作列 -->
                        <el-table-column label="操作" width="150" align="center">
                            <template>
                                <el-button size="mini" type="primary" @click="handleCheck()">审核</el-button>
                                <el-button size="mini" type="primary">详情</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        :page-sizes="[10, 20, 30, 50]"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :page-size="queryParams.size"
                        :current-page="queryParams.page"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import { getDateRange } from 'snbcCommon/common/picker-options.js';
import formulaTitle from '../components/formulaTitle.vue';

// 日期区间选择配置
const dateTypeOptions = [
    { dateType: '全周期', dateRange: getDateRange('today') },
    { dateType: '上一年度', dateRange: getDateRange('lastYear') },
    { dateType: '本年度', dateRange: getDateRange('thisYear') },
    { dateType: '最近一年', dateRange: getDateRange('lastYear') }
];

export default {
    name: 'PlannedManagement',
    components: { formulaTitle },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            searchTitle: '查询条件',
            planTitle: '计划列表',
            form: {
                dateType: '全周期',
                requestDate: getDateRange('today')
            },
            dateTypeOptions,
            activeTab: 'table1',
            // 按目标表格调整列配置：名称、prop 与宽度
            columns: [
                { label: '需求编号', prop: 'demandNo', width: '100' },
                { label: '计划名称', prop: 'planName', width: '140' },
                { label: '计划开始日期', prop: 'planStartDate', width: '140' },
                { label: '计划完成日期', prop: 'planEndDate', width: '140' },
                { label: '提交日期', prop: 'submitDate', width: '140' },
                { label: '提交人', prop: 'submitter', width: '100' },
                { label: '计划状态', prop: 'planStatus', width: '100' },
                { label: '审核状态', prop: 'auditStatus', width: '100' },
                { label: '审核人', prop: 'auditor', width: '100' },
                { label: '审核日期', prop: 'auditDate', width: '140' },
                { label: '审核意见', prop: 'auditOpinion', width: '300' }
            ],
            // 按目标表格调整 tab 与数据：字段对齐、填充模拟数据
            tabList: [
                {
                    label: '所有',
                    name: 'table1',
                    data: [
                        {
                            demandNo: 'xxxx-1',
                            planName: '信息化V2.7.0',
                            planStartDate: '2025-05-04',
                            planEndDate: '2025-05-30',
                            submitDate: '2025-04-24',
                            submitter: '于淼',
                            planStatus: '完成',
                            auditStatus: '待审核',
                            auditor: '于淼',
                            auditDate: '',
                            auditOpinion: ''
                        },
                        {
                            demandNo: 'xxxx-2',
                            planName: '信息化V2.6.0',
                            planStartDate: '2025-04-01',
                            planEndDate: '2025-04-28',
                            submitDate: '2025-03-28',
                            submitter: '于淼',
                            planStatus: '关闭',
                            auditStatus: '冲刺成功',
                            auditor: '于淼',
                            auditDate: '2025-05-06',
                            auditOpinion: ''
                        }
                    ]
                },
                {
                    label: '结论待定',
                    name: 'table2',
                    data: []
                },
                {
                    label: '冲刺成功',
                    name: 'table3',
                    data: []
                },
                {
                    label: '冲刺失败',
                    name: 'table4',
                    data: []
                }
            ],
            // 分页
            queryParams: {
                page: 1,
                size: 20
            },
            total: 0
        };
    },
    methods: {
        handleRadio(dateType) {
            const target = this.dateTypeOptions.find((item) => item.dateType === dateType);
            this.$set(this.form, 'requestDate', target.dateRange);
        },
        // 分页
        sizeChange(newSize) {
            this.queryParams.size = newSize;
            this.fetchData();
        },
        currentChange(newPage) {
            this.queryParams.page = newPage;
            this.fetchData();
        },
        handleCheck() {
            this.$router.push({
                path: './SprintCheck'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .query-area {
    margin-top: 10px;
    .area-search {
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
</style>
