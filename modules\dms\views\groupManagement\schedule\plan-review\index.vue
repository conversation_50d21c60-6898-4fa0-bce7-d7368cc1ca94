<!-- 计划审核 -->
<template>
    <div class="view">
        <div class="content">
            <formula-title :title="searchTitle"></formula-title>
            <el-form class="query-area" :model="form" label-width="100px">
                <div class="area-search">
                    <el-form-item label="快捷查询">
                        <el-radio-group v-model="form.dateType" size="medium" @input="handleRadio">
                            <el-radio-button
                                v-for="(item, index) in dateTypeOptions"
                                :key="index"
                                :label="item.dateType"
                                >{{ item.dateType }}</el-radio-button
                            >
                        </el-radio-group>
                        <el-date-picker
                            v-model="form.requestDate"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            size="small"
                            style="width: 300px; margin-left: 10px"
                            :unlink-panels="true"
                        ></el-date-picker>
                    </el-form-item>
                    <div>
                        <el-button type="primary" icon="el-icon-search" size="mini">查询</el-button>
                        <el-button icon="el-icon-refresh-right" size="mini">重置</el-button>
                    </div>
                </div>
            </el-form>
            <formula-title :title="planTitle"></formula-title>
            <el-tabs v-model="activeTab">
                <el-tab-pane v-for="(tab, index) in tabList" :key="index" :label="tab.label" :name="tab.name">
                    <el-table :data="data" style="width: 100%" border>
                        <!-- 按目标表格调整 columns 与 prop 映射 -->
                        <el-table-column
                            v-for="(column, index) in columns"
                            :key="index"
                            :label="column.label"
                            :prop="column.prop"
                            :width="column.width"
                            :sortable="column.sortable"
                            align="center"
                        ></el-table-column>
                        <!-- 操作列 -->
                        <el-table-column label="操作" width="230" align="center">
                            <template>
                                <el-button size="mini" type="primary" @click="handleCheck()">审核</el-button>
                                <el-button size="mini" type="primary" @click="handleDetails()">详情</el-button>
                                <el-button size="mini" type="primary" @click="handleChange()">变更</el-button>
                                <el-button size="mini" type="primary">归档</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        :page-sizes="[10, 20, 30, 50]"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :page-size="queryParams.size"
                        :current-page="queryParams.page"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import { getDateRange } from 'snbcCommon/common/picker-options.js';
import formulaTitle from '../components/formulaTitle.vue';

// 日期区间选择配置
const dateTypeOptions = [
    { dateType: '全周期', dateRange: getDateRange('today') },
    { dateType: '上一年度', dateRange: getDateRange('lastYear') },
    { dateType: '本年度', dateRange: getDateRange('thisYear') },
    { dateType: '最近一年', dateRange: getDateRange('lastYear') }
];

export default {
    name: 'PlannedManagement',
    components: { formulaTitle },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            searchTitle: '查询条件',
            planTitle: '计划列表',
            form: {
                dateType: '全周期',
                requestDate: getDateRange('today')
            },
            dateTypeOptions,
            columns: [
                { label: '序号', type: 'index', width: 60 },
                { label: '计划名称', prop: 'planName', width: 160, sortable: true },
                { label: '版本', prop: 'version', width: 80, sortable: 'true' },
                { label: '计划开始日期', prop: 'planStartDate', width: 130, sortable: 'true' },
                { label: '计划完成日期', prop: 'planEndDate', width: 120 },
                { label: '版本状态', prop: 'versionStatus', width: 100 },
                { label: '审批人', prop: 'approver', width: 100 },
                { label: '审批日期', prop: 'approveDate', width: 120 },
                { label: '审核结果', prop: 'auditResult', width: 120, sortable: 'true' },
                { label: '申请人', prop: 'applicant', width: 100 },
                { label: '申请日期', prop: 'applyDate', width: 120 }
            ],
            tabList: [
                { label: '所有', name: 'all' },
                { label: '审批中', name: 'approving' },
                { label: '使用中', name: 'inUse' },
                { label: '待修改', name: 'toModify' },
                { label: '已归档', name: 'archived' }
            ],
            data: [
                {
                    planName: '信息化V2.7.0',
                    version: 'V3',
                    planStartDate: '2025/6/1',
                    planEndDate: '2025/7/15',
                    versionStatus: '审批中',
                    approver: '张三',
                    approveDate: '',
                    auditResult: '待定',
                    applicant: '',
                    applyDate: ''
                },
                {
                    planName: '信息化V2.7.0',
                    version: 'V3',
                    planStartDate: '2025/6/1',
                    planEndDate: '2025/7/15',
                    versionStatus: '待修改',
                    approver: '张三',
                    approveDate: '',
                    auditResult: '拒绝',
                    applicant: '',
                    applyDate: ''
                },
                {
                    planName: '信息化V2.7.0',
                    version: 'V2',
                    planStartDate: '2025/6/1',
                    planEndDate: '2025/7/15',
                    versionStatus: '使用中',
                    approver: '张三',
                    approveDate: '',
                    auditResult: '通过',
                    applicant: '',
                    applyDate: ''
                },
                {
                    planName: '信息化V2.7.0',
                    version: 'V1',
                    planStartDate: '2025/6/1',
                    planEndDate: '2025/6/30',
                    versionStatus: '已归档',
                    approver: '张三',
                    approveDate: '',
                    auditResult: '通过',
                    applicant: '',
                    applyDate: ''
                }
            ],
            activeTab: 'all',
            // 分页
            queryParams: {
                page: 1,
                size: 20
            },
            total: 0
        };
    },
    methods: {
        handleRadio(dateType) {
            const target = this.dateTypeOptions.find((item) => item.dateType === dateType);
            this.$set(this.form, 'requestDate', target.dateRange);
        },
        // 分页
        sizeChange(newSize) {
            this.queryParams.size = newSize;
            this.fetchData();
        },
        currentChange(newPage) {
            this.queryParams.page = newPage;
            this.fetchData();
        },
        // 计划版本详情
        handleDetails() {
            this.$router.push({
                path: './PlanVersion'
            });
        },
        // 审核
        handleCheck() {
            this.$router.push({
                path: './PlanChange'
            });
        },
        // 变更
        handleChange() {
            this.$router.push({
                path: './PlanAdjust'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .query-area {
    margin-top: 10px;
    .area-search {
        height: 50px;
        display: flex;
        justify-content: space-between;
    }
}
</style>
